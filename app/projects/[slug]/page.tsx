"use client"

import { notFound } from "next/navigation"
import Image from "next/image"
import Link from "next/link"
import { <PERSON>Lef<PERSON>, <PERSON>ith<PERSON>, ExternalLink } from "lucide-react"
import { motion } from "framer-motion"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { getProjectBySlug } from "@/data/projects"
import { ThemeToggle } from "@/components/theme-toggle"
import { AnimatedGradientText } from "@/components/animated-gradient-text"
import { AnimatedBackground } from "@/components/animated-background"
import { FloatingShapes } from "@/components/floating-shapes"
import { ScrollReveal } from "@/components/scroll-reveal"
import { ProjectMobileNav } from "@/components/project-mobile-nav"
import { useMobile } from "@/hooks/use-mobile"

interface ProjectPageProps {
  params: {
    slug: string
  }
}

export default function ProjectPage({ params }: ProjectPageProps) {
  const project = getProjectBySlug(params.slug)
  const isMobile = useMobile()

  if (!project) {
    notFound()
  }

  return (
    <div className="flex flex-col min-h-screen">
      <header className="sticky top-0 z-40 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-16 items-center space-x-4 sm:justify-between sm:space-x-0">
          <div className="flex gap-6 md:gap-10">
            <Link href="/" className="flex items-center space-x-2">
              <span className="inline-block font-bold">DevOps Engineer</span>
            </Link>
          </div>
          <div className="flex flex-1 items-center justify-end space-x-4">
            <ThemeToggle />
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }} className="hidden md:block">
              <Link href="/#projects">
                <Button variant="ghost" size="sm" className="gap-2">
                  <ArrowLeft className="h-4 w-4" />
                  Back to Projects
                </Button>
              </Link>
            </motion.div>
            <ProjectMobileNav />
          </div>
        </div>
      </header>

      <main className="flex-1">
        <div className="py-8 md:py-12 bg-gradient-to-b from-primary to-secondary dark:from-gray-900 dark:to-gray-800 text-primary-foreground">
          <AnimatedBackground variant="gradient" className="w-full">
            <FloatingShapes count={10} />
            <div className="container px-4 md:px-6">
              <div className="mx-auto max-w-4xl">
                <motion.div
                  className="mb-8 space-y-4"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  <div className="flex flex-wrap gap-2">
                    {project.tags.map((tag, i) => (
                      <motion.div
                        key={tag}
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: i * 0.1, duration: 0.3 }}
                      >
                        <Badge variant="secondary" className="bg-background/20 text-primary-foreground border-primary-foreground/10">
                          {tag}
                        </Badge>
                      </motion.div>
                    ))}
                  </div>
                  <h1 className="text-2xl font-bold tracking-tight sm:text-4xl md:text-5xl text-primary-foreground">
                    <AnimatedGradientText text={project.title} />
                  </h1>
                  <p className="text-base sm:text-xl text-primary-foreground/80">{project.description}</p>
                  <div className="flex flex-wrap gap-3 sm:gap-4">
                    <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                      <Button
                        asChild
                        className="bg-background/20 text-primary-foreground border border-primary-foreground/20 hover:bg-background/30 text-xs sm:text-sm px-3 sm:px-4"
                        size={isMobile ? "sm" : "default"}
                      >
                        <Link href={project.githubUrl} target="_blank" rel="noreferrer" className="gap-2">
                          <Github className="h-3 w-3 sm:h-4 sm:w-4" />
                          View Code
                        </Link>
                      </Button>
                    </motion.div>
                    {project.demoUrl && (
                      <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                        <Button
                          variant="outline"
                          asChild
                          className="bg-transparent text-primary-foreground border-primary-foreground/70 hover:bg-background/20 text-xs sm:text-sm px-3 sm:px-4"
                          size={isMobile ? "sm" : "default"}
                        >
                          <Link href={project.demoUrl} target="_blank" rel="noreferrer" className="gap-2">
                            <ExternalLink className="h-3 w-3 sm:h-4 sm:w-4" />
                            Live Demo
                          </Link>
                        </Button>
                      </motion.div>
                    )}
                  </div>
                </motion.div>
              </div>
            </div>
          </AnimatedBackground>
        </div>

        <div className="container px-4 py-8 md:px-6 md:py-12">
          <div className="mx-auto max-w-4xl">
            <ScrollReveal>
              <div className="relative aspect-video w-full overflow-hidden rounded-lg border mb-8 shadow-xl">
                <Image
                  src={project.image || "/placeholder.svg"}
                  alt={project.title}
                  fill
                  className="object-cover"
                  priority
                />
              </div>
            </ScrollReveal>

            <div className="space-y-8">
              <ScrollReveal>
                <div className="prose max-w-none">
                  <h2 className="text-2xl font-bold">Overview</h2>
                  <p className="text-muted-foreground">{project.longDescription}</p>
                </div>
              </ScrollReveal>

              <Separator className="bg-gradient-to-r from-primary/20 to-secondary/20" />

              <AnimatedBackground variant="dots">
                <div className="grid gap-6 sm:gap-8">
                  <ScrollReveal direction="left">
                    <Card className="card-hover border-primary/10">
                      <CardContent className="p-4 sm:p-6">
                        <h2 className="text-xl sm:text-2xl font-bold mb-3 sm:mb-4 text-primary">Challenges</h2>
                        <ul className="space-y-2">
                          {project.challenges.map((challenge, index) => (
                            <motion.li
                              key={index}
                              className="flex gap-2 text-sm sm:text-base"
                              initial={{ opacity: 0, x: isMobile ? 0 : -20, y: isMobile ? -10 : 0 }}
                              animate={{ opacity: 1, x: 0, y: 0 }}
                              transition={{ delay: index * 0.1, duration: 0.3 }}
                            >
                              <span className="text-primary flex-shrink-0">•</span>
                              <span>{challenge}</span>
                            </motion.li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  </ScrollReveal>

                  <ScrollReveal direction="right">
                    <Card className="card-hover border-secondary/10">
                      <CardContent className="p-4 sm:p-6">
                        <h2 className="text-xl sm:text-2xl font-bold mb-3 sm:mb-4 text-secondary">Solutions</h2>
                        <ul className="space-y-2">
                          {project.solutions.map((solution, index) => (
                            <motion.li
                              key={index}
                              className="flex gap-2 text-sm sm:text-base"
                              initial={{ opacity: 0, x: isMobile ? 0 : 20, y: isMobile ? 10 : 0 }}
                              animate={{ opacity: 1, x: 0, y: 0 }}
                              transition={{ delay: index * 0.1, duration: 0.3 }}
                            >
                              <span className="text-secondary flex-shrink-0">•</span>
                              <span>{solution}</span>
                            </motion.li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  </ScrollReveal>
                </div>
              </AnimatedBackground>

              <Separator className="bg-gradient-to-r from-primary/20 to-secondary/20" />

              <ScrollReveal>
                <div>
                  <h2 className="text-xl sm:text-2xl font-bold mb-3 sm:mb-4">Results & Impact</h2>
                  <Card className="card-hover">
                    <CardContent className="p-4 sm:p-6">
                      <ul className="space-y-2">
                        {project.results.map((result, index) => (
                          <motion.li
                            key={index}
                            className="flex gap-2 text-sm sm:text-base"
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: index * 0.1, duration: 0.3 }}
                          >
                            <span className="text-primary flex-shrink-0">•</span>
                            <span>{result}</span>
                          </motion.li>
                        ))}
                      </ul>
                    </CardContent>
                  </Card>
                </div>
              </ScrollReveal>

              <Separator className="bg-gradient-to-r from-primary/20 to-secondary/20" />

              <div className="bg-muted/50 dark:bg-gray-900/50 rounded-lg">
                <ScrollReveal>
                  <div className="p-4 sm:p-8">
                    <h2 className="text-xl sm:text-2xl font-bold mb-3 sm:mb-4">Technologies Used</h2>
                    <div className="space-y-4">
                      {project.technologies.map((tech, index) => (
                        <motion.div
                          key={index}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: index * 0.1, duration: 0.3 }}
                        >
                          <h3 className="font-semibold text-base sm:text-lg mb-2 text-primary">{tech.category}</h3>
                          <div className="flex flex-wrap gap-1.5 sm:gap-2">
                            {tech.items.map((item, i) => (
                              <motion.div
                                key={item}
                                initial={{ opacity: 0, scale: 0.8 }}
                                animate={{ opacity: 1, scale: 1 }}
                                transition={{ delay: i * 0.05 + index * 0.1, duration: 0.3 }}
                              >
                                <Badge
                                  variant="outline"
                                  className="shadow-sm bg-background/80 text-xs sm:text-sm py-0.5 px-1.5 sm:px-2"
                                >
                                  {item}
                                </Badge>
                              </motion.div>
                            ))}
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </div>
                </ScrollReveal>
              </div>

              <Separator className="bg-gradient-to-r from-primary/20 to-secondary/20" />

              <ScrollReveal>
                <div>
                  <h2 className="text-xl sm:text-2xl font-bold mb-3 sm:mb-4">Screenshots & Visuals</h2>
                  <div className="grid gap-4 sm:gap-6 md:grid-cols-2">
                    {project.screenshots.map((screenshot, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.2, duration: 0.5 }}
                        whileHover={{ y: -5 }}
                        className="touch-manipulation"
                      >
                        <Card className="overflow-hidden card-hover">
                          <div className="relative aspect-video w-full">
                            <Image
                              src={screenshot.url || "/placeholder.svg"}
                              alt={screenshot.caption}
                              fill
                              className="object-cover"
                              sizes="(max-width: 768px) 100vw, 50vw"
                            />
                          </div>
                          <CardContent className="p-3 sm:p-4">
                            <p className="text-xs sm:text-sm text-muted-foreground">{screenshot.caption}</p>
                          </CardContent>
                        </Card>
                      </motion.div>
                    ))}
                  </div>
                </div>
              </ScrollReveal>
            </div>
          </div>
        </div>
      </main>

      <footer className="w-full border-t py-6">
        <div className="container flex flex-col items-center justify-between gap-4 md:h-16 md:flex-row">
          <p className="text-sm text-muted-foreground">© {new Date().getFullYear()} John Doe. All rights reserved.</p>
          <div className="flex items-center gap-4">
            <Link href="/#contact" className="text-sm text-muted-foreground hover:text-primary transition-colors">
              Contact Me
            </Link>
          </div>
        </div>
      </footer>
    </div>
  )
}
