"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ArrowRight, Download, FileText, Github, Gitlab, Linkedin, Mail, Server, Twitter } from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import ContactForm from "@/components/contact-form"
import ProjectCard from "@/components/project-card"
import ExperienceTimeline from "@/components/experience-timeline"
import SkillsCloud from "@/components/skills-cloud"
import { ThemeToggle } from "@/components/theme-toggle"
import { AnimatedGradientText } from "@/components/animated-gradient-text"
import { AnimatedBackground } from "@/components/animated-background"
import { FloatingShapes } from "@/components/floating-shapes"
import { TestimonialsSection } from "@/components/testimonials-section"
import { MobileNav } from "@/components/mobile-nav"
import { ScrollReveal } from "@/components/scroll-reveal"
import { ProfilePicture } from "@/components/profile-picture"
import { PersonalInterests } from "@/components/personal-interests"
import { motion } from "framer-motion"
import { projects } from "@/data/projects"
import { useMobile } from "@/hooks/use-mobile"

export default function Home() {
  const isMobile = useMobile()

  return (
    <div className="flex flex-col min-h-screen">
      <header className="sticky top-0 z-40 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-16 items-center space-x-4 sm:justify-between sm:space-x-0">
          <div className="flex gap-6 md:gap-10">
            <Link href="/" className="flex items-center space-x-2">
              <motion.div whileHover={{ rotate: 360 }} transition={{ duration: 0.5 }}>
                <Server className="h-6 w-6 text-primary" />
              </motion.div>
              <span className="inline-block font-bold">DevOps Engineer</span>
            </Link>
            <nav className="hidden md:flex gap-6">
              <Link
                href="#about"
                className="flex items-center text-sm font-medium text-muted-foreground transition-colors hover:text-primary"
              >
                About
              </Link>
              <Link
                href="#experience"
                className="flex items-center text-sm font-medium text-muted-foreground transition-colors hover:text-primary"
              >
                Experience
              </Link>
              <Link
                href="#projects"
                className="flex items-center text-sm font-medium text-muted-foreground transition-colors hover:text-primary"
              >
                Projects
              </Link>
              <Link
                href="#testimonials"
                className="flex items-center text-sm font-medium text-muted-foreground transition-colors hover:text-primary"
              >
                Testimonials
              </Link>
              <Link
                href="#contact"
                className="flex items-center text-sm font-medium text-muted-foreground transition-colors hover:text-primary"
              >
                Contact
              </Link>
            </nav>
          </div>
          <div className="flex flex-1 items-center justify-end space-x-1">
            <ThemeToggle />
            <div className="hidden md:flex items-center space-x-1">
              <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
                <Link href="https://github.com/oussamalakhdar" target="_blank" rel="noreferrer">
                  <div className="rounded-full p-2 text-muted-foreground hover:bg-muted hover:text-foreground">
                    <Github className="h-5 w-5" />
                    <span className="sr-only">GitHub</span>
                  </div>
                </Link>
              </motion.div>
              <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
                <Link href="https://linkedin.com/in/oussama-lakhdar" target="_blank" rel="noreferrer">
                  <div className="rounded-full p-2 text-muted-foreground hover:bg-muted hover:text-foreground">
                    <Linkedin className="h-5 w-5" />
                    <span className="sr-only">LinkedIn</span>
                  </div>
                </Link>
              </motion.div>
              <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
                <Link href="https://twitter.com" target="_blank" rel="noreferrer">
                  <div className="rounded-full p-2 text-muted-foreground hover:bg-muted hover:text-foreground">
                    <Twitter className="h-5 w-5" />
                    <span className="sr-only">Twitter</span>
                  </div>
                </Link>
              </motion.div>
            </div>
            <MobileNav />
          </div>
        </div>
      </header>
      <main className="flex-1">
        <div className="w-full py-12 md:py-24 lg:py-32 xl:py-48 bg-gradient-to-b from-blue-600 to-blue-800 dark:from-gray-900 dark:to-gray-800 text-white">
          <AnimatedBackground variant="gradient" className="w-full">
            <FloatingShapes count={20} />
            <div className="container px-4 md:px-6">
              <div className="grid gap-8 lg:grid-cols-[1fr_400px] lg:gap-16 xl:grid-cols-[1fr_500px]">
                <div className="flex flex-col justify-center space-y-6">
                  <motion.div
                    className="space-y-3"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                  >
                    <h1 className="text-3xl font-bold tracking-tighter sm:text-5xl xl:text-6xl/none text-white">
                      <AnimatedGradientText text="Oussama Lakhdar" variant="blue" className="text-white" />
                    </h1>
                    <h2 className="text-xl font-medium text-white/90 sm:text-2xl">
                      <motion.span
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: 0.3, duration: 0.5 }}
                      >
                        Cloud DevOps Engineer
                      </motion.span>
                    </h2>
                    <motion.p
                      className="max-w-[600px] text-white/80 md:text-xl leading-relaxed"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.5, duration: 0.5 }}
                    >
                      Specializing in CI/CD automation, Kubernetes orchestration, and cloud infrastructure.
                      Focused on building scalable, reliable systems with Docker, Terraform, and AWS.
                    </motion.p>
                  </motion.div>

                  <motion.div
                    className="flex flex-wrap gap-3"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.7, duration: 0.3 }}
                  >
                    {["Docker Specialist", "Kubernetes", "CI/CD Engineer"].map((skill, index) => (
                      <motion.div
                        key={skill}
                        className="flex items-center gap-1 bg-white/10 backdrop-blur-sm px-3 py-1.5 rounded-full text-sm text-white/90"
                        initial={{ opacity: 0, y: 20, scale: 0.8 }}
                        animate={{ opacity: 1, y: 0, scale: 1 }}
                        transition={{
                          delay: 0.8 + index * 0.1,
                          duration: 0.4,
                          type: "spring",
                          stiffness: 300,
                          damping: 20
                        }}
                        whileHover={{
                          scale: 1.1,
                          backgroundColor: "rgba(255, 255, 255, 0.25)",
                          y: -2
                        }}
                        transition={{ type: "spring", stiffness: 400, damping: 17 }}
                      >
                        <span>{skill}</span>
                      </motion.div>
                    ))}
                  </motion.div>

                  <motion.div
                    className="flex flex-col gap-3 sm:flex-row sm:gap-4"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.9, duration: 0.5 }}
                  >
                    <motion.div
                      whileHover={{ scale: 1.05, y: -2 }}
                      whileTap={{ scale: 0.95 }}
                      transition={{ type: "spring", stiffness: 400, damping: 17 }}
                    >
                      <Link href="#contact">
                        <Button size="lg" className="gap-2 bg-white/20 text-white border border-white/20 hover:bg-white/30 hover:shadow-lg transition-all duration-200">
                          Contact Me <ArrowRight className="h-4 w-4" />
                        </Button>
                      </Link>
                    </motion.div>
                    <motion.div
                      whileHover={{ scale: 1.05, y: -2 }}
                      whileTap={{ scale: 0.95 }}
                      transition={{ type: "spring", stiffness: 400, damping: 17 }}
                    >
                      <Link href="/resume.pdf" target="_blank" rel="noopener noreferrer">
                        <Button size="lg" variant="outline" className="gap-2 bg-white/10 text-white border-white/50 hover:bg-white/20 hover:border-white/70 hover:shadow-lg transition-all duration-200">
                          <FileText className="h-4 w-4" /> Download Resume
                        </Button>
                      </Link>
                    </motion.div>
                    <motion.div
                      whileHover={{ scale: 1.05, y: -2 }}
                      whileTap={{ scale: 0.95 }}
                      transition={{ type: "spring", stiffness: 400, damping: 17 }}
                    >
                      <Link href="#projects">
                        <Button size="lg" variant="outline" className="gap-2 bg-transparent text-white border-white/50 hover:bg-white/20 hover:border-white/70 hover:shadow-lg transition-all duration-200">
                          View Projects
                        </Button>
                      </Link>
                    </motion.div>
                  </motion.div>
                </div>
                <div className="relative flex items-center justify-center lg:justify-end">
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{
                      opacity: 1,
                      x: 0,
                      transition: {
                        duration: 0.8,
                        delay: 0.4,
                        ease: "easeOut"
                      }
                    }}
                  >
                    <ProfilePicture showDownloadButton={false} />
                  </motion.div>
                </div>
              </div>
            </div>
          </AnimatedBackground>
        </div>

        <div className="w-full py-12 md:py-24 lg:py-32 bg-accent dark:bg-gray-950">
          <AnimatedBackground variant="dots" className="w-full">
            <div className="container px-4 md:px-6" id="about">
              <ScrollReveal>
                <div className="flex flex-col items-center justify-center space-y-4 text-center">
                  <div className="space-y-2">
                    <div className="inline-block rounded-lg bg-muted px-3 py-1 text-sm">About Me</div>
                    <h2 className="text-3xl font-bold tracking-tighter sm:text-5xl">
                      <AnimatedGradientText text="DevOps Professional" delay={0.1} variant="primary" />
                    </h2>
                    <p className="max-w-[900px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                      With over 8 years of experience in DevOps and cloud infrastructure, I specialize in building and
                      optimizing CI/CD pipelines, containerization, and cloud-native solutions. My expertise spans
                      across AWS, Azure, Kubernetes, Docker, Terraform, and various monitoring and logging tools.
                    </p>
                  </div>
                </div>
              </ScrollReveal>

              <div className="mx-auto max-w-5xl py-8">
                <ScrollReveal delay={0.2}>
                  <Card className="card-hover bg-background/60 backdrop-blur-sm border-primary/10">
                    <CardContent className="p-6">
                      <div className="flex flex-col space-y-4">
                        <p className="text-foreground/90 leading-relaxed">
                          I'm passionate about creating efficient, scalable, and resilient infrastructure that enables development teams to deliver value faster.
                          My approach combines technical expertise with a deep understanding of DevOps culture and methodologies.
                        </p>
                        <p className="text-foreground/90 leading-relaxed">
                          Throughout my career, I've helped organizations of all sizes—from startups to enterprises—modernize their infrastructure,
                          implement automation, and adopt cloud-native technologies. I believe in infrastructure as code, continuous delivery,
                          and building systems that are observable, maintainable, and secure by design.
                        </p>
                        <p className="text-foreground/90 leading-relaxed">
                          When I'm not automating infrastructure or optimizing pipelines, you'll find me contributing to open-source projects,
                          mentoring junior engineers, or exploring the latest technologies in the cloud-native ecosystem.
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                </ScrollReveal>
              </div>

              <div className="mx-auto grid max-w-5xl grid-cols-1 gap-6 py-8 md:grid-cols-2 lg:gap-12">
                <ScrollReveal delay={0.3}>
                  <Card className="card-hover h-full">
                    <CardContent className="p-6">
                      <div className="flex flex-col space-y-4">
                        <h3 className="text-2xl font-bold">Technical Skills</h3>
                        <SkillsCloud />
                      </div>
                    </CardContent>
                  </Card>
                </ScrollReveal>
                <ScrollReveal delay={0.4}>
                  <Card className="card-hover h-full">
                    <CardContent className="p-6">
                      <div className="flex flex-col space-y-4">
                        <h3 className="text-2xl font-bold">Education & Certifications</h3>
                        <div className="space-y-5">
                          <div>
                            <h4 className="font-semibold text-lg">Software Developer</h4>
                            <p className="text-sm text-muted-foreground">1337 - Mohammed VI Polytechnic University</p>
                            <p className="text-sm mt-1">Benguerir, Morocco</p>
                          </div>
                          <div className="pt-2">
                            <h4 className="font-semibold text-lg mb-2">Technical Skills</h4>
                            <div className="flex flex-wrap gap-2">
                              <Badge variant="secondary" className="px-2.5 py-1">Docker</Badge>
                              <Badge variant="secondary" className="px-2.5 py-1">Kubernetes</Badge>
                              <Badge variant="secondary" className="px-2.5 py-1">AWS</Badge>
                              <Badge variant="secondary" className="px-2.5 py-1">Terraform</Badge>
                              <Badge variant="secondary" className="px-2.5 py-1">Ansible</Badge>
                              <Badge variant="secondary" className="px-2.5 py-1">Jenkins</Badge>
                              <Badge variant="secondary" className="px-2.5 py-1">GitHub Actions</Badge>
                              <Badge variant="secondary" className="px-2.5 py-1">ArgoCD</Badge>
                              <Badge variant="secondary" className="px-2.5 py-1">Linux</Badge>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </ScrollReveal>
              </div>

              <div className="mx-auto max-w-5xl py-8">
                <ScrollReveal delay={0.5}>
                  <div className="space-y-4">
                    <h3 className="text-2xl font-bold text-center">Personal Interests</h3>
                    <p className="text-center text-muted-foreground max-w-[700px] mx-auto mb-6">
                      When I'm not immersed in the world of DevOps and cloud technologies, here's what keeps me busy:
                    </p>
                    <PersonalInterests />
                  </div>
                </ScrollReveal>
              </div>
            </div>
          </AnimatedBackground>
        </div>

        <div className="w-full py-12 md:py-24 lg:py-32 bg-muted dark:bg-gray-900">
          <div className="container px-4 md:px-6" id="experience">
            <ScrollReveal>
              <div className="flex flex-col items-center justify-center space-y-4 text-center">
                <div className="space-y-2">
                  <div className="inline-block rounded-lg bg-background dark:bg-muted px-3 py-1 text-sm">Experience</div>
                  <h2 className="text-3xl font-bold tracking-tighter sm:text-5xl">
                    <AnimatedGradientText text="Professional Journey" delay={0.1} variant="teal" />
                  </h2>
                  <p className="max-w-[900px] text-foreground/80 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                    My career path in DevOps and infrastructure automation.
                  </p>
                </div>
              </div>
            </ScrollReveal>
            <ScrollReveal delay={0.2}>
              <div className="mx-auto max-w-3xl py-12">
                <ExperienceTimeline />
              </div>
            </ScrollReveal>
          </div>
        </div>

        <div className="w-full py-12 md:py-24 lg:py-32 bg-accent dark:bg-gray-950">
          <AnimatedBackground variant="dots" className="w-full">
            <div className="container px-4 md:px-6" id="projects">
              <ScrollReveal>
                <div className="flex flex-col items-center justify-center space-y-4 text-center">
                  <div className="space-y-2">
                    <div className="inline-block rounded-lg bg-muted px-3 py-1 text-sm">Projects</div>
                    <h2 className="text-3xl font-bold tracking-tighter sm:text-5xl">
                      <AnimatedGradientText text="Featured Work" delay={0.1} variant="purple" />
                    </h2>
                    <p className="max-w-[900px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                      Explore some of my key DevOps and infrastructure projects.
                    </p>
                  </div>
                </div>
              </ScrollReveal>
              <div className="mx-auto grid max-w-5xl grid-cols-1 gap-6 py-12 md:grid-cols-2 lg:grid-cols-3">
                {projects.slice(0, 6).map((project, index) => (
                  <ProjectCard
                    key={project.id}
                    id={project.id}
                    title={project.title}
                    description={project.description}
                    tags={project.tags}
                    image={project.image}
                    githubUrl={project.githubUrl}
                    demoUrl={project.demoUrl}
                    index={index}
                  />
                ))}
              </div>
            </div>
          </AnimatedBackground>
        </div>

        <div className="w-full py-12 md:py-24 lg:py-32 bg-muted dark:bg-gray-900" id="testimonials">
          <div className="container px-4 md:px-6">
            <ScrollReveal>
              <div className="flex flex-col items-center justify-center space-y-4 text-center mb-12">
                <div className="space-y-2">
                  <div className="inline-block rounded-lg bg-background dark:bg-muted px-3 py-1 text-sm">Testimonials</div>
                  <h2 className="text-3xl font-bold tracking-tighter sm:text-5xl">
                    <AnimatedGradientText text="Client Feedback" delay={0.1} variant="secondary" />
                  </h2>
                  <p className="max-w-[900px] text-foreground/80 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                    What professionals say about working with me.
                  </p>
                </div>
              </div>
            </ScrollReveal>
            <ScrollReveal delay={0.2}>
              <div className="mx-auto max-w-5xl">
                <TestimonialsSection />
              </div>
            </ScrollReveal>
          </div>
        </div>

        <div className="w-full py-12 md:py-24 lg:py-32 bg-gradient-to-b from-indigo-600 to-blue-700 dark:from-gray-800 dark:to-gray-900 text-white">
          <AnimatedBackground variant="waves" className="w-full">
            <div className="container px-4 md:px-6" id="contact">
              <ScrollReveal>
                <div className="flex flex-col items-center justify-center space-y-4 text-center">
                  <div className="space-y-2">
                    <div className="inline-block rounded-lg bg-white/20 px-3 py-1 text-sm text-white">Contact</div>
                    <h2 className="text-3xl font-bold tracking-tighter sm:text-5xl text-white">
                      <AnimatedGradientText text="Get In Touch" delay={0.1} variant="accent" className="text-white" />
                    </h2>
                    <p className="max-w-[900px] text-white/80 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                      Have a project in mind or want to discuss DevOps opportunities? Reach out to me.
                    </p>
                  </div>
                </div>
              </ScrollReveal>
              <div className="mx-auto grid max-w-5xl grid-cols-1 gap-6 py-12 lg:grid-cols-2">
                <ScrollReveal delay={0.2}>
                  <div className="flex flex-col justify-center space-y-4">
                    <div className="space-y-2">
                      <h3 className="text-xl font-bold text-white">Contact Information</h3>
                      <p className="text-white/80">Feel free to reach out through any of these channels:</p>
                    </div>
                    <div className="space-y-4">
                      <motion.div
                        className="flex items-center gap-2 group"
                        whileHover={{ x: 5 }}
                        transition={{ type: "spring", stiffness: 400, damping: 10 }}
                      >
                        <Mail className="h-5 w-5 text-white" />
                        <a
                          href="mailto:<EMAIL>"
                          className="text-white/80 group-hover:text-white transition-colors"
                        >
                          <EMAIL>
                        </a>
                      </motion.div>
                      <motion.div
                        className="flex items-center gap-2 group"
                        whileHover={{ x: 5 }}
                        transition={{ type: "spring", stiffness: 400, damping: 10 }}
                      >
                        <Linkedin className="h-5 w-5 text-white" />
                        <a
                          href="https://linkedin.com/in/oussama-lakhdar"
                          target="_blank"
                          rel="noreferrer"
                          className="text-white/80 group-hover:text-white transition-colors"
                        >
                          linkedin.com/in/oussama-lakhdar
                        </a>
                      </motion.div>
                      <motion.div
                        className="flex items-center gap-2 group"
                        whileHover={{ x: 5 }}
                        transition={{ type: "spring", stiffness: 400, damping: 10 }}
                      >
                        <Github className="h-5 w-5 text-white" />
                        <a
                          href="https://github.com/oussamalakhdar"
                          target="_blank"
                          rel="noreferrer"
                          className="text-white/80 group-hover:text-white transition-colors"
                        >
                          github.com/oussamalakhdar
                        </a>
                      </motion.div>
                      <motion.div
                        className="flex items-center gap-2 group"
                        whileHover={{ x: 5 }}
                        transition={{ type: "spring", stiffness: 400, damping: 10 }}
                      >
                        <Gitlab className="h-5 w-5 text-white" />
                        <a
                          href="https://gitlab.com/oussamalakhdar"
                          target="_blank"
                          rel="noreferrer"
                          className="text-white/80 group-hover:text-white transition-colors"
                        >
                          gitlab.com/oussamalakhdar
                        </a>
                      </motion.div>
                      <motion.div
                        className="flex items-center gap-2 group"
                        whileHover={{ x: 5 }}
                        transition={{ type: "spring", stiffness: 400, damping: 10 }}
                      >
                        <Twitter className="h-5 w-5 text-white" />
                        <a
                          href="https://twitter.com/oussamalakhdar"
                          target="_blank"
                          rel="noreferrer"
                          className="text-white/80 group-hover:text-white transition-colors"
                        >
                          @oussamalakhdar
                        </a>
                      </motion.div>
                    </div>
                  </div>
                </ScrollReveal>
                <ScrollReveal delay={0.3}>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.4 }}
                  >
                    <Card className="shadow-lg bg-background/95">
                      <CardContent className="p-6">
                        <ContactForm />
                      </CardContent>
                    </Card>
                  </motion.div>
                </ScrollReveal>
              </div>
            </div>
          </AnimatedBackground>
        </div>
      </main>
      <footer className="w-full border-t py-6 md:py-0">
        <div className="container flex flex-col items-center justify-between gap-4 md:h-16 md:flex-row">
          <p className="text-sm text-muted-foreground">© {new Date().getFullYear()} Oussama Lakhdar. All rights reserved.</p>
          <div className="flex items-center gap-4">
            <Link href="#" className="text-sm text-muted-foreground hover:text-primary transition-colors">
              Privacy Policy
            </Link>
            <Link href="#" className="text-sm text-muted-foreground hover:text-primary transition-colors">
              Terms of Service
            </Link>
          </div>
        </div>
      </footer>
    </div>
  )
}
