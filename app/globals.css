@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 220 14% 96%;
    --foreground: 222 47% 11%;

    --card: 0 0% 100%;
    --card-foreground: 222 47% 11%;

    --popover: 0 0% 100%;
    --popover-foreground: 222 47% 11%;

    --primary: 215 28% 17%;
    --primary-foreground: 222 47% 11%;

    --secondary: 215 27% 32%;
    --secondary-foreground: 222 47% 11%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 220 14% 96%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 215 28% 17%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 222 47% 11%;
    --foreground: 210 40% 98%;

    --card: 222 47% 11%;
    --card-foreground: 210 40% 98%;

    --popover: 222 47% 11%;
    --popover-foreground: 210 40% 98%;

    --primary: 215 28% 17%;
    --primary-foreground: 210 40% 98%;

    --secondary: 215 27% 32%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 215 28% 17%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  .gradient-heading {
    @apply bg-clip-text text-transparent;
  }

  :root .gradient-heading {
    @apply bg-gradient-to-r from-primary via-primary/80 to-secondary bg-clip-text;
  }

  .dark .gradient-heading {
    @apply bg-gradient-to-r from-gray-300 to-white bg-clip-text;
  }

  .shimmer {
    @apply bg-gradient-to-r from-transparent via-muted-foreground/10 to-transparent bg-[length:200%_100%] animate-shimmer;
  }

  .card-hover {
    @apply transition-all duration-300 hover:shadow-lg hover:shadow-primary/10 hover:-translate-y-1;
  }
}
