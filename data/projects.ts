export interface Project {
  id: string
  title: string
  description: string
  longDescription: string
  tags: string[]
  image: string
  githubUrl: string
  demoUrl?: string
  challenges: string[]
  solutions: string[]
  results: string[]
  technologies: {
    category: string
    items: string[]
  }[]
  screenshots: {
    url: string
    caption: string
  }[]
}

export const projects: Project[] = [
  {
    id: "cloud-migration-framework",
    title: "Cloud Migration Framework",
    description:
      "Developed a framework for migrating legacy applications to AWS with zero downtime, reducing operational costs by 40%.",
    longDescription:
      "The Cloud Migration Framework is a comprehensive solution designed to streamline the process of migrating legacy applications to AWS cloud infrastructure. This framework addresses the common challenges organizations face during cloud migrations, including minimizing downtime, ensuring data integrity, optimizing costs, and maintaining security compliance throughout the transition.",
    tags: ["AWS", "Terraform", "CI/CD", "Python"],
    image: "/placeholder.svg?height=400&width=600",
    githubUrl: "https://github.com",
    challenges: [
      "Legacy applications with complex dependencies and minimal documentation",
      "Strict requirement for zero downtime during migration",
      "Compliance requirements for sensitive data handling",
      "Integration with existing monitoring and alerting systems",
      "Tight timeline with limited resources",
    ],
    solutions: [
      "Developed an automated discovery tool to map application dependencies",
      "Implemented a blue-green deployment strategy with automated rollback capabilities",
      "Created a secure data migration pipeline with encryption at rest and in transit",
      "Built custom Terraform modules for standardized infrastructure provisioning",
      "Designed a phased migration approach with comprehensive testing at each stage",
    ],
    results: [
      "Reduced operational costs by 40% through optimized resource utilization",
      "Achieved zero downtime during migration of 15+ critical applications",
      "Improved application performance by 35% through cloud-native optimizations",
      "Decreased time-to-market for new features by 60%",
      "Enhanced security posture with automated compliance checks",
    ],
    technologies: [
      {
        category: "Cloud Infrastructure",
        items: ["AWS EC2", "AWS RDS", "AWS S3", "AWS Lambda", "AWS CloudFront", "AWS VPC"],
      },
      {
        category: "Infrastructure as Code",
        items: ["Terraform", "AWS CloudFormation", "Pulumi"],
      },
      {
        category: "CI/CD & Automation",
        items: ["Jenkins", "GitHub Actions", "AWS CodePipeline", "Python", "Bash"],
      },
      {
        category: "Monitoring & Logging",
        items: ["AWS CloudWatch", "Prometheus", "Grafana", "ELK Stack"],
      },
    ],
    screenshots: [
      {
        url: "/placeholder.svg?height=600&width=800",
        caption: "Migration dashboard showing real-time progress and status",
      },
      {
        url: "/placeholder.svg?height=600&width=800",
        caption: "Infrastructure architecture diagram in AWS",
      },
      {
        url: "/placeholder.svg?height=600&width=800",
        caption: "Performance comparison before and after migration",
      },
    ],
  },
  {
    id: "kubernetes-monitoring-stack",
    title: "Kubernetes Monitoring Stack",
    description:
      "Implemented a comprehensive monitoring solution for Kubernetes clusters using Prometheus, Grafana, and Loki.",
    longDescription:
      "The Kubernetes Monitoring Stack is an end-to-end observability solution designed for modern containerized applications running on Kubernetes. This project addresses the challenges of gaining comprehensive visibility into the health, performance, and behavior of microservices architectures by integrating best-in-class open-source monitoring tools into a cohesive, scalable platform.",
    tags: ["Kubernetes", "Prometheus", "Grafana", "Helm"],
    image: "/placeholder.svg?height=400&width=600",
    githubUrl: "https://github.com",
    challenges: [
      "Monitoring distributed microservices across multiple Kubernetes clusters",
      "Handling high cardinality metrics without performance degradation",
      "Implementing effective alerting with minimal false positives",
      "Ensuring monitoring infrastructure scales with application growth",
      "Providing meaningful dashboards for both technical and non-technical stakeholders",
    ],
    solutions: [
      "Deployed Prometheus with federation for scalable metrics collection",
      "Implemented Loki for centralized log aggregation with label-based querying",
      "Created Grafana dashboards with drill-down capabilities for different user personas",
      "Developed custom exporters for application-specific metrics",
      "Used Helm charts for consistent deployment across environments",
    ],
    results: [
      "Reduced MTTR (Mean Time To Resolution) by 65% through improved observability",
      "Achieved 99.9% monitoring system uptime even during cluster scaling events",
      "Eliminated alert fatigue by reducing false positives by 80%",
      "Enabled data-driven capacity planning, preventing 3 potential outages",
      "Improved cross-team collaboration with shared observability platform",
    ],
    technologies: [
      {
        category: "Monitoring & Observability",
        items: ["Prometheus", "Grafana", "Loki", "Tempo", "AlertManager", "kube-state-metrics", "node-exporter"],
      },
      {
        category: "Container Orchestration",
        items: ["Kubernetes", "Helm", "Kustomize"],
      },
      {
        category: "Infrastructure",
        items: ["AWS EKS", "GCP GKE", "Terraform"],
      },
      {
        category: "Service Mesh",
        items: ["Istio", "Linkerd"],
      },
    ],
    screenshots: [
      {
        url: "/placeholder.svg?height=600&width=800",
        caption: "Cluster overview dashboard in Grafana",
      },
      {
        url: "/placeholder.svg?height=600&width=800",
        caption: "Alert management interface with incident history",
      },
      {
        url: "/placeholder.svg?height=600&width=800",
        caption: "Log correlation with metrics for troubleshooting",
      },
    ],
  },
  {
    id: "gitops-pipeline-automation",
    title: "GitOps Pipeline Automation",
    description:
      "Built a GitOps workflow using ArgoCD and GitHub Actions, enabling continuous deployment with automated rollbacks.",
    longDescription:
      "The GitOps Pipeline Automation project implements a modern approach to continuous delivery that uses Git as the single source of truth for declarative infrastructure and application configuration. This system automates the entire deployment lifecycle, from code commit to production deployment, while maintaining security, auditability, and reliability through a pull-based deployment model.",
    tags: ["GitOps", "ArgoCD", "GitHub Actions", "Kubernetes"],
    image: "/placeholder.svg?height=400&width=600",
    githubUrl: "https://github.com",
    challenges: [
      "Managing deployments across multiple environments and clusters",
      "Ensuring configuration consistency between environments",
      "Implementing secure promotion workflows with appropriate approvals",
      "Handling secrets management in a GitOps workflow",
      "Providing visibility into deployment status and history",
    ],
    solutions: [
      "Implemented ArgoCD for declarative, Git-based delivery to Kubernetes",
      "Created a multi-stage GitHub Actions workflow with environment-specific configurations",
      "Developed a promotion model with required reviews for production deployments",
      "Integrated Sealed Secrets for secure secret management in Git",
      "Built custom dashboards for deployment visibility and audit trails",
    ],
    results: [
      "Reduced deployment time from hours to minutes across all environments",
      "Achieved 100% configuration consistency between environments",
      "Eliminated 95% of manual deployment tasks, freeing up developer time",
      "Improved security posture with enforced review processes",
      "Enabled instant rollbacks, reducing impact of problematic deployments by 80%",
    ],
    technologies: [
      {
        category: "GitOps & CI/CD",
        items: ["ArgoCD", "Flux", "GitHub Actions", "Tekton", "Kustomize"],
      },
      {
        category: "Container Orchestration",
        items: ["Kubernetes", "Helm", "Kustomize"],
      },
      {
        category: "Security",
        items: ["Sealed Secrets", "Vault", "OPA Gatekeeper", "RBAC"],
      },
      {
        category: "Infrastructure",
        items: ["AWS EKS", "Terraform", "AWS CodeBuild"],
      },
    ],
    screenshots: [
      {
        url: "/placeholder.svg?height=600&width=800",
        caption: "ArgoCD dashboard showing application sync status",
      },
      {
        url: "/placeholder.svg?height=600&width=800",
        caption: "GitHub Actions workflow visualization",
      },
      {
        url: "/placeholder.svg?height=600&width=800",
        caption: "Deployment history with rollback capabilities",
      },
    ],
  },
  {
    id: "infrastructure-as-code-library",
    title: "Infrastructure as Code Library",
    description:
      "Created a reusable IaC library with Terraform modules for standardized cloud resource provisioning across teams.",
    longDescription:
      "The Infrastructure as Code Library is a comprehensive collection of reusable Terraform modules designed to standardize and simplify cloud resource provisioning across multiple teams and projects. This library implements organizational best practices, security controls, and compliance requirements as code, enabling consistent infrastructure deployment while allowing teams to maintain velocity and autonomy.",
    tags: ["Terraform", "AWS", "Azure", "IaC"],
    image: "/placeholder.svg?height=400&width=600",
    githubUrl: "https://github.com",
    challenges: [
      "Inconsistent infrastructure provisioning across teams",
      "Ensuring compliance with security and regulatory requirements",
      "Balancing standardization with team autonomy",
      "Managing module versioning and backward compatibility",
      "Providing comprehensive documentation for diverse user groups",
    ],
    solutions: [
      "Developed a modular Terraform library with consistent interfaces",
      "Implemented security and compliance controls as code within modules",
      "Created flexible modules with sensible defaults but configurable options",
      "Established semantic versioning with automated testing for each release",
      "Built comprehensive documentation with examples for common use cases",
    ],
    results: [
      "Reduced time to provision new environments by 75%",
      "Achieved 100% compliance with security standards across all deployments",
      "Decreased cloud infrastructure costs by 30% through standardized patterns",
      "Enabled self-service infrastructure for development teams",
      "Simplified onboarding process for new team members",
    ],
    technologies: [
      {
        category: "Infrastructure as Code",
        items: ["Terraform", "Terragrunt", "AWS CDK", "Pulumi"],
      },
      {
        category: "Cloud Providers",
        items: ["AWS", "Azure", "GCP"],
      },
      {
        category: "Testing & Validation",
        items: ["Terratest", "Checkov", "tfsec", "Sentinel"],
      },
      {
        category: "CI/CD",
        items: ["GitHub Actions", "Atlantis", "Terraform Cloud"],
      },
    ],
    screenshots: [
      {
        url: "/placeholder.svg?height=600&width=800",
        caption: "Module architecture and dependency diagram",
      },
      {
        url: "/placeholder.svg?height=600&width=800",
        caption: "Infrastructure deployment visualization",
      },
      {
        url: "/placeholder.svg?height=600&width=800",
        caption: "Compliance dashboard showing security posture",
      },
    ],
  },
  {
    id: "serverless-microservices-platform",
    title: "Serverless Microservices Platform",
    description: "Architected a serverless platform for microservices using AWS Lambda, API Gateway, and DynamoDB.",
    longDescription:
      "The Serverless Microservices Platform is a cloud-native architecture designed to enable rapid development and deployment of scalable, event-driven microservices without managing traditional server infrastructure. This platform leverages AWS serverless technologies to provide a flexible foundation for building modern applications with automatic scaling, pay-per-use pricing, and reduced operational overhead.",
    tags: ["Serverless", "AWS Lambda", "DynamoDB", "Microservices"],
    image: "/placeholder.svg?height=400&width=600",
    githubUrl: "https://github.com",
    challenges: [
      "Designing for stateless, event-driven architecture",
      "Managing cold starts and performance optimization",
      "Implementing effective service discovery and API management",
      "Ensuring data consistency across distributed services",
      "Developing a comprehensive monitoring and debugging strategy",
    ],
    solutions: [
      "Implemented domain-driven design principles for service boundaries",
      "Used provisioned concurrency and code optimization for critical paths",
      "Created an API Gateway with custom authorizers and usage plans",
      "Designed event-sourcing patterns with DynamoDB streams",
      "Deployed X-Ray tracing with custom subsegments for observability",
    ],
    results: [
      "Reduced infrastructure costs by 60% compared to container-based solution",
      "Achieved sub-100ms response times for 95% of API requests",
      "Eliminated operational overhead for scaling during traffic spikes",
      "Decreased time-to-market for new features by 50%",
      "Improved developer productivity with streamlined deployment process",
    ],
    technologies: [
      {
        category: "Serverless Technologies",
        items: ["AWS Lambda", "AWS API Gateway", "AWS Step Functions", "AWS EventBridge", "AWS SQS", "AWS SNS"],
      },
      {
        category: "Data Storage",
        items: ["DynamoDB", "S3", "ElastiCache", "Aurora Serverless"],
      },
      {
        category: "Development & Deployment",
        items: ["Serverless Framework", "AWS SAM", "TypeScript", "Node.js"],
      },
      {
        category: "Monitoring & Observability",
        items: ["AWS CloudWatch", "X-Ray", "Lumigo", "Sentry"],
      },
    ],
    screenshots: [
      {
        url: "/placeholder.svg?height=600&width=800",
        caption: "Serverless architecture diagram",
      },
      {
        url: "/placeholder.svg?height=600&width=800",
        caption: "API performance metrics dashboard",
      },
      {
        url: "/placeholder.svg?height=600&width=800",
        caption: "Distributed tracing visualization",
      },
    ],
  },
  {
    id: "container-security-scanner",
    title: "Container Security Scanner",
    description:
      "Developed an automated container security scanning tool integrated with CI/CD pipelines to detect vulnerabilities.",
    longDescription:
      "The Container Security Scanner is an automated security solution designed to identify vulnerabilities, misconfigurations, and compliance issues in container images and Kubernetes deployments. This tool integrates directly into CI/CD pipelines to provide early detection of security issues, preventing vulnerable containers from reaching production environments and ensuring consistent security standards across all deployments.",
    tags: ["Docker", "Security", "CI/CD", "Python"],
    image: "/placeholder.svg?height=400&width=600",
    githubUrl: "https://github.com",
    challenges: [
      "Balancing thorough security scanning with CI/CD pipeline performance",
      "Managing false positives without compromising security",
      "Integrating with multiple container registries and CI/CD platforms",
      "Providing actionable remediation guidance for identified issues",
      "Ensuring compliance with industry security standards",
    ],
    solutions: [
      "Implemented parallel scanning with prioritized vulnerability assessment",
      "Developed a machine learning model to reduce false positives over time",
      "Created flexible adapters for major container registries and CI platforms",
      "Built an automated remediation suggestion engine with code examples",
      "Mapped findings to CIS Benchmarks, NIST, and other compliance frameworks",
    ],
    results: [
      "Reduced vulnerable containers in production by 95%",
      "Decreased mean time to remediate vulnerabilities by 70%",
      "Achieved compliance with SOC 2 and ISO 27001 security requirements",
      "Integrated with 5 major CI/CD platforms with minimal performance impact",
      "Enabled security self-service for development teams",
    ],
    technologies: [
      {
        category: "Container Security",
        items: ["Trivy", "Clair", "Anchore", "Falco", "OPA Conftest"],
      },
      {
        category: "Development",
        items: ["Python", "Go", "gRPC", "REST APIs"],
      },
      {
        category: "CI/CD Integration",
        items: ["Jenkins", "GitHub Actions", "GitLab CI", "CircleCI", "ArgoCD"],
      },
      {
        category: "Reporting & Analytics",
        items: ["Elasticsearch", "Kibana", "Grafana", "PostgreSQL"],
      },
    ],
    screenshots: [
      {
        url: "/placeholder.svg?height=600&width=800",
        caption: "Vulnerability dashboard with severity breakdown",
      },
      {
        url: "/placeholder.svg?height=600&width=800",
        caption: "CI/CD pipeline integration showing security gates",
      },
      {
        url: "/placeholder.svg?height=600&width=800",
        caption: "Remediation recommendations with code examples",
      },
    ],
  },
]

export function getProjectBySlug(slug: string): Project | undefined {
  return projects.find((project) => project.id === slug)
}

export function getAllProjectIds(): string[] {
  return projects.map((project) => project.id)
}
