"use client"

import { motion } from "framer-motion"
import Image from "next/image"
import { useMobile } from "@/hooks/use-mobile"
import { useTheme } from "next-themes"
import { useEffect, useState } from "react"
import { Download } from "lucide-react"
import { Button } from "@/components/ui/button"
import Link from "next/link"

interface ProfilePictureProps {
  className?: string
  showDownloadButton?: boolean
}

export function ProfilePicture({ className, showDownloadButton = false }: ProfilePictureProps) {
  const isMobile = useMobile()
  const { theme } = useTheme()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return (
      <div className={`${className || ""} relative`}>
        <div
          className={`relative overflow-hidden rounded-full border-4 border-primary/20 bg-background shadow-lg ${
            isMobile ? "w-[120px] h-[120px]" : "w-[220px] h-[220px]"
          }`}
        />
      </div>
    )
  }

  return (
    <motion.div
      className={`${className || ""} relative`}
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{
        opacity: 1,
        scale: 1,
        transition: {
          type: "spring",
          stiffness: 100,
          damping: 15,
          duration: 0.8
        }
      }}
    >
      <motion.div
        className={`relative overflow-hidden rounded-full border-4 ${
          theme === "dark" ? "border-primary-foreground/20" : "border-primary/20"
        } bg-background shadow-lg ${isMobile ? "w-[120px] h-[120px]" : "w-[220px] h-[220px]"}`}
        initial={{ rotate: -5 }}
        animate={{
          rotate: 0,
          transition: {
            type: "spring",
            stiffness: 100,
            damping: 15,
            delay: 0.2
          }
        }}
        whileHover={{
          scale: 1.05,
          boxShadow: "0 15px 30px rgba(0, 0, 0, 0.2)",
          borderColor: theme === "dark" ? "rgba(255, 255, 255, 0.3)" : "rgba(0, 0, 0, 0.3)",
          transition: {
            type: "spring",
            stiffness: 300,
            damping: 20
          }
        }}
      >
        <Image
          src="/images/profile.png"
          alt="Profile"
          fill
          className="object-cover"
          priority
        />
      </motion.div>

      {showDownloadButton && (
        <motion.div
          className="absolute -bottom-4 left-1/2 transform -translate-x-1/2"
          initial={{ opacity: 0, y: 10 }}
          animate={{
            opacity: 1,
            y: 0,
            transition: {
              delay: 0.6,
              duration: 0.3,
              type: "spring",
              stiffness: 300,
              damping: 20
            }
          }}
          whileHover={{
            scale: 1.05,
            y: -2,
            transition: {
              type: "spring",
              stiffness: 400,
              damping: 10
            }
          }}
        >
          <Link href="/resume.pdf" target="_blank" rel="noopener noreferrer">
            <Button
              size="sm"
              variant="secondary"
              className="shadow-md flex items-center gap-1 font-medium"
            >
              <Download className="h-3 w-3" />
              <span className="text-xs">Resume</span>
            </Button>
          </Link>
        </motion.div>
      )}
    </motion.div>
  )
}
