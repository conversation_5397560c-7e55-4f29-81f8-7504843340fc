"use client"

import { motion } from "framer-motion"
import Image from "next/image"
import { useMobile } from "@/hooks/use-mobile"
import { useTheme } from "next-themes"
import { useEffect, useState } from "react"
import { Download } from "lucide-react"
import { Button } from "@/components/ui/button"
import Link from "next/link"

interface ProfilePictureProps {
  className?: string
  showDownloadButton?: boolean
}

export function ProfilePicture({ className, showDownloadButton = false }: ProfilePictureProps) {
  const isMobile = useMobile()
  const { theme } = useTheme()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return (
      <div className={`${className || ""} relative`}>
        <div
          className={`relative overflow-hidden rounded-full border-4 border-gray-200 bg-background shadow-xl ${
            isMobile ? "w-[180px] h-[180px]" : "w-[300px] h-[300px]"
          }`}
        />
      </div>
    )
  }

  return (
    <motion.div
      className={`${className || ""} relative`}
      initial={{ opacity: 0 }}
      animate={{
        opacity: 1,
        transition: {
          duration: 0.6,
          ease: "easeOut"
        }
      }}
    >
      <div
        className={`relative overflow-hidden rounded-full border-4 ${
          theme === "dark" ? "border-white/10" : "border-gray-200"
        } bg-background shadow-xl ${isMobile ? "w-[180px] h-[180px]" : "w-[300px] h-[300px]"}`}
      >
        <Image
          src="/images/profile.png"
          alt="Oussama Lakhdar - Cloud DevOps Engineer"
          fill
          className="object-cover"
          priority
        />
      </div>

      {showDownloadButton && (
        <motion.div
          className="absolute -bottom-6 left-1/2 transform -translate-x-1/2"
          initial={{ opacity: 0 }}
          animate={{
            opacity: 1,
            transition: {
              delay: 0.8,
              duration: 0.4,
              ease: "easeOut"
            }
          }}
        >
          <Link href="/resume.pdf" target="_blank" rel="noopener noreferrer">
            <Button
              size="sm"
              variant="secondary"
              className="shadow-lg flex items-center gap-2 font-medium hover:shadow-xl transition-shadow duration-200"
            >
              <Download className="h-4 w-4" />
              <span className="text-sm">Resume</span>
            </Button>
          </Link>
        </motion.div>
      )}
    </motion.div>
  )
}
