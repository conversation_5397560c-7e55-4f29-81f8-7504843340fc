"use client"

import { useTheme } from "next-themes"
import { useEffect, useState } from "react"
import { motion } from "framer-motion"

interface FloatingShapesProps {
  count?: number
  className?: string
}

export function FloatingShapes({ count = 10, className }: FloatingShapesProps) {
  const { theme } = useTheme()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) return null

  const shapes = Array.from({ length: count }).map((_, i) => {
    const size = Math.random() * 80 + 40 // Increased size
    const left = Math.random() * 100
    const top = Math.random() * 100
    const delay = Math.random() * 5
    const duration = Math.random() * 10 + 10
    const opacity = Math.random() * 0.15 + 0.1 // Increased opacity
    const shape = Math.floor(Math.random() * 3)
    const color = theme === "dark" ? "255, 255, 255" : "0, 0, 0"

    const yMovement = Math.random() * 50 + 20
    const xMovement = Math.random() * 50 + 20

    return { id: i, size, left, top, delay, duration, opacity, shape, color, yMovement, xMovement }
  })

  return (
    <div className={`absolute inset-0 overflow-hidden pointer-events-none ${className}`}>
      {shapes.map((shape) => (
        <motion.div
          key={shape.id}
          className="absolute rounded-full"
          style={{
            width: `${shape.size}px`,
            height: `${shape.size}px`,
            left: `${shape.left}%`,
            top: `${shape.top}%`,
            backgroundColor: `rgba(${shape.color}, ${shape.opacity})`,
          }}
          initial={{ opacity: 0 }}
          animate={{
            opacity: shape.opacity,
            y: [0, shape.yMovement, 0],
            x: shape.id % 2 === 0 ? [0, shape.xMovement, 0] : [0, -shape.xMovement, 0],
          }}
          transition={{
            opacity: { duration: 1, delay: shape.delay * 0.2 },
            y: {
              duration: shape.duration,
              repeat: Number.POSITIVE_INFINITY,
              ease: "easeInOut",
              delay: shape.delay,
            },
            x: {
              duration: shape.duration * 1.3,
              repeat: Number.POSITIVE_INFINITY,
              ease: "easeInOut",
              delay: shape.delay,
            },
          }}
        />
      ))}
    </div>
  )
}
