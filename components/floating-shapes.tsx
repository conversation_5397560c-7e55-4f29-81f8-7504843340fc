"use client"

import { useTheme } from "next-themes"
import { useEffect, useState } from "react"
import { motion } from "framer-motion"

interface FloatingShapesProps {
  count?: number
  className?: string
}

export function FloatingShapes({ count = 10, className }: FloatingShapesProps) {
  const { theme } = useTheme()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) return null

  const shapes = Array.from({ length: count }).map((_, i) => {
    const size = Math.random() * 60 + 30 // Slightly smaller size
    const left = Math.random() * 80 + 10 // Keep within 10-90% to prevent edge clipping
    const top = Math.random() * 80 + 10 // Keep within 10-90% to prevent edge clipping
    const delay = Math.random() * 5
    const duration = Math.random() * 10 + 15 // Slower movement
    const opacity = Math.random() * 0.2 + 0.15 // More visible opacity
    const shape = Math.floor(Math.random() * 3)
    const color = theme === "dark" ? "255, 255, 255" : "0, 0, 0"

    const yMovement = Math.random() * 30 + 15 // Visible movement range
    const xMovement = Math.random() * 30 + 15 // Visible movement range

    return { id: i, size, left, top, delay, duration, opacity, shape, color, yMovement, xMovement }
  })

  return (
    <div className={`absolute inset-0 overflow-hidden pointer-events-none ${className}`}>
      <div className="absolute inset-4"> {/* Add padding to prevent edge clipping */}
        {shapes.map((shape) => (
          <motion.div
            key={shape.id}
            className="absolute rounded-full" // Remove blur for better visibility
            style={{
              width: `${shape.size}px`,
              height: `${shape.size}px`,
              left: `${shape.left}%`,
              top: `${shape.top}%`,
              backgroundColor: `rgba(${shape.color}, ${shape.opacity})`,
              transform: `translate(-50%, -50%)`, // Center the shapes on their position
            }}
            initial={{ opacity: 0, scale: 0.5 }}
            animate={{
              opacity: shape.opacity,
              scale: 1,
              y: [0, shape.yMovement, 0],
              x: shape.id % 2 === 0 ? [0, shape.xMovement, 0] : [0, -shape.xMovement, 0],
            }}
            transition={{
              opacity: { duration: 1.5, delay: shape.delay * 0.3 },
              scale: { duration: 1.5, delay: shape.delay * 0.3, ease: "easeOut" },
              y: {
                duration: shape.duration,
                repeat: Number.POSITIVE_INFINITY,
                ease: "easeInOut",
                delay: shape.delay,
              },
              x: {
                duration: shape.duration * 1.2,
                repeat: Number.POSITIVE_INFINITY,
                ease: "easeInOut",
                delay: shape.delay,
              },
            }}
          />
        ))}
      </div>
    </div>
  )
}
