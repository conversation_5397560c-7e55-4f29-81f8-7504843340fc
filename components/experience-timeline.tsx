import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

export default function ExperienceTimeline() {
  const experiences = [
    {
      title: "Senior DevOps Engineer",
      company: "Tech Innovations Inc.",
      period: "2020 - Present",
      description:
        "Leading a team of DevOps engineers to implement and maintain CI/CD pipelines, Kubernetes clusters, and cloud infrastructure across multiple projects.",
      achievements: [
        "Reduced deployment time by 70% through pipeline optimization",
        "Implemented GitOps workflow that improved release reliability by 45%",
        "Migrated 15 applications to Kubernetes, improving scalability and reducing costs by 30%",
      ],
      technologies: ["Kubernetes", "AWS", "Terraform", "ArgoCD", "GitHub Actions", "Prometheus"],
    },
    {
      title: "DevOps Engineer",
      company: "Cloud Solutions Ltd.",
      period: "2017 - 2020",
      description:
        "Designed and implemented infrastructure as code solutions and CI/CD pipelines for enterprise clients.",
      achievements: [
        "Built a multi-cloud disaster recovery solution with 99.99% uptime",
        "Automated infrastructure provisioning, reducing setup time from days to hours",
        "Implemented container security scanning, reducing vulnerabilities by 80%",
      ],
      technologies: ["AWS", "Azure", "Docker", "Jenkins", "Ansible", "Terraform"],
    },
    {
      title: "Systems Administrator",
      company: "Global Data Corp.",
      period: "2015 - 2017",
      description: "Managed on-premises and cloud infrastructure, focusing on automation and monitoring solutions.",
      achievements: [
        "Migrated legacy applications to AWS, improving performance by 40%",
        "Implemented monitoring solutions that reduced incident response time by 60%",
        "Automated routine maintenance tasks, saving 15 hours per week",
      ],
      technologies: ["Linux", "AWS", "Bash", "Python", "Nagios", "VMware"],
    },
  ]

  return (
    <div className="space-y-8">
      {experiences.map((exp, index) => (
        <div key={index} className="relative pl-6 pb-8">
          {index !== experiences.length - 1 && (
            <div className="absolute top-6 left-2 bottom-0 w-0.5 bg-muted-foreground/20"></div>
          )}
          <div className="absolute top-6 left-0 w-4 h-4 rounded-full border-2 border-primary bg-background"></div>
          <Card>
            <CardContent className="p-6">
              <div className="space-y-4">
                <div>
                  <div className="flex items-center justify-between">
                    <h3 className="text-xl font-bold">{exp.title}</h3>
                    <Badge variant="outline">{exp.period}</Badge>
                  </div>
                  <p className="text-muted-foreground">{exp.company}</p>
                </div>
                <p>{exp.description}</p>
                <div>
                  <h4 className="font-semibold mb-2">Key Achievements:</h4>
                  <ul className="list-disc pl-5 space-y-1">
                    {exp.achievements.map((achievement, i) => (
                      <li key={i} className="text-sm">
                        {achievement}
                      </li>
                    ))}
                  </ul>
                </div>
                <div className="flex flex-wrap gap-2">
                  {exp.technologies.map((tech) => (
                    <Badge key={tech} variant="secondary">
                      {tech}
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      ))}
    </div>
  )
}
