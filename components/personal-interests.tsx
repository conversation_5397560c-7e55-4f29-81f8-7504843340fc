"use client"

import { Card, CardContent } from "@/components/ui/card"
import { motion } from "framer-motion"
import { 
  Bike, 
  BookOpen, 
  Code, 
  Coffee, 
  Compass, 
  Gamepad2, 
  Headphones, 
  Mountain, 
  Plane 
} from "lucide-react"

interface Interest {
  name: string
  icon: React.ReactNode
  description: string
}

export function PersonalInterests() {
  const interests: Interest[] = [
    {
      name: "Travel",
      icon: <Plane className="h-5 w-5" />,
      description: "Exploring new cultures and destinations around the world."
    },
    {
      name: "Hiking",
      icon: <Mountain className="h-5 w-5" />,
      description: "Conquering trails and enjoying nature's beauty."
    },
    {
      name: "Coding",
      icon: <Code className="h-5 w-5" />,
      description: "Building side projects and learning new technologies."
    },
    {
      name: "Reading",
      icon: <BookOpen className="h-5 w-5" />,
      description: "Technical books and science fiction novels."
    },
    {
      name: "Coffee",
      icon: <Coffee className="h-5 w-5" />,
      description: "Exploring specialty coffee from around the world."
    },
    {
      name: "<PERSON>",
      icon: <Bike className="h-5 w-5" />,
      description: "Weekend rides and occasional long-distance tours."
    },
    {
      name: "Music",
      icon: <Headphones className="h-5 w-5" />,
      description: "Listening to indie rock and electronic music."
    },
    {
      name: "Gaming",
      icon: <Gamepad2 className="h-5 w-5" />,
      description: "Strategy games and immersive RPGs."
    },
    {
      name: "Exploration",
      icon: <Compass className="h-5 w-5" />,
      description: "Finding hidden gems in my own city."
    },
  ]

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
      {interests.map((interest, index) => (
        <motion.div
          key={interest.name}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: index * 0.1 }}
        >
          <Card className="h-full hover:shadow-md transition-shadow duration-300">
            <CardContent className="p-4 flex flex-col items-center text-center">
              <motion.div 
                className="p-2 rounded-full bg-primary/10 mb-3 mt-2"
                whileHover={{ scale: 1.1, rotate: 5 }}
                transition={{ type: "spring", stiffness: 400, damping: 10 }}
              >
                {interest.icon}
              </motion.div>
              <h3 className="font-medium mb-1">{interest.name}</h3>
              <p className="text-sm text-muted-foreground">{interest.description}</p>
            </CardContent>
          </Card>
        </motion.div>
      ))}
    </div>
  )
}
