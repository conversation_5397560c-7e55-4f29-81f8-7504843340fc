"use client"

import { Badge } from "@/components/ui/badge"
import { motion } from "framer-motion"
import { useState } from "react"

export default function SkillsCloud() {
  const [hoveredSkill, setHoveredSkill] = useState<string | null>(null)

  const skills = [
    { name: "AWS", category: "cloud", proficiency: 95 },
    { name: "Azure", category: "cloud", proficiency: 85 },
    { name: "GCP", category: "cloud", proficiency: 80 },
    { name: "Kubernetes", category: "container", proficiency: 90 },
    { name: "Docker", category: "container", proficiency: 95 },
    { name: "Terraform", category: "iac", proficiency: 90 },
    { name: "Ansible", category: "iac", proficiency: 85 },
    { name: "<PERSON>", category: "cicd", proficiency: 90 },
    { name: "GitHub Actions", category: "cicd", proficiency: 95 },
    { name: "GitLab CI", category: "cicd", proficiency: 85 },
    { name: "ArgoCD", category: "gitops", proficiency: 90 },
    { name: "Flux", category: "gitops", proficiency: 80 },
    { name: "Prometheus", category: "monitoring", proficiency: 90 },
    { name: "<PERSON><PERSON>", category: "monitoring", proficiency: 85 },
    { name: "ELK Stack", category: "logging", proficiency: 85 },
    { name: "Python", category: "programming", proficiency: 90 },
    { name: "Go", category: "programming", proficiency: 75 },
    { name: "Bash", category: "programming", proficiency: 85 },
    { name: "Linux", category: "os", proficiency: 95 },
    { name: "Networking", category: "infrastructure", proficiency: 80 },
    { name: "Security", category: "security", proficiency: 85 },
    { name: "Microservices", category: "architecture", proficiency: 90 },
    { name: "Serverless", category: "architecture", proficiency: 85 },
    { name: "CI/CD", category: "process", proficiency: 95 },
    { name: "Infrastructure as Code", category: "process", proficiency: 90 },
    { name: "Site Reliability Engineering", category: "process", proficiency: 85 },
  ]

  // Group skills by category
  const categories = {
    cloud: "Cloud Platforms",
    container: "Containerization",
    iac: "Infrastructure as Code",
    cicd: "CI/CD",
    gitops: "GitOps",
    monitoring: "Monitoring",
    logging: "Logging",
    programming: "Programming",
    os: "Operating Systems",
    infrastructure: "Infrastructure",
    security: "Security",
    architecture: "Architecture",
    process: "Processes & Methodologies",
  }

  const categoryColors = {
    cloud: "bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 border-blue-200 dark:border-blue-800",
    container: "bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300 border-purple-200 dark:border-purple-800",
    iac: "bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 border-green-200 dark:border-green-800",
    cicd: "bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-300 border-orange-200 dark:border-orange-800",
    gitops: "bg-indigo-100 dark:bg-indigo-900/30 text-indigo-800 dark:text-indigo-300 border-indigo-200 dark:border-indigo-800",
    monitoring: "bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300 border-red-200 dark:border-red-800",
    logging: "bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300 border-yellow-200 dark:border-yellow-800",
    programming: "bg-teal-100 dark:bg-teal-900/30 text-teal-800 dark:text-teal-300 border-teal-200 dark:border-teal-800",
    os: "bg-gray-100 dark:bg-gray-900/30 text-gray-800 dark:text-gray-300 border-gray-200 dark:border-gray-800",
    infrastructure: "bg-cyan-100 dark:bg-cyan-900/30 text-cyan-800 dark:text-cyan-300 border-cyan-200 dark:border-cyan-800",
    security: "bg-rose-100 dark:bg-rose-900/30 text-rose-800 dark:text-rose-300 border-rose-200 dark:border-rose-800",
    architecture: "bg-amber-100 dark:bg-amber-900/30 text-amber-800 dark:text-amber-300 border-amber-200 dark:border-amber-800",
    process: "bg-emerald-100 dark:bg-emerald-900/30 text-emerald-800 dark:text-emerald-300 border-emerald-200 dark:border-emerald-800",
  }

  const groupedSkills = skills.reduce(
    (acc, skill) => {
      const category = skill.category as keyof typeof categories
      if (!acc[category]) {
        acc[category] = []
      }
      acc[category].push(skill)
      return acc
    },
    {} as Record<string, typeof skills>,
  )

  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.05,
      },
    },
  }

  const item = {
    hidden: { opacity: 0, y: 10 },
    show: { opacity: 1, y: 0 },
  }

  return (
    <div className="space-y-6">
      {Object.entries(groupedSkills).map(([category, skills], categoryIndex) => (
        <motion.div
          key={category}
          className="space-y-3"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: categoryIndex * 0.1 }}
        >
          <h4 className="text-sm font-medium text-foreground flex items-center">
            <div className={`w-2 h-2 rounded-full mr-2 ${categoryColors[category as keyof typeof categoryColors].split(' ').slice(0, 2).join(' ')}`}></div>
            {categories[category as keyof typeof categories]}
          </h4>
          <motion.div
            className="flex flex-wrap gap-2"
            variants={container}
            initial="hidden"
            animate="show"
          >
            {skills.map((skill, index) => (
              <motion.div
                key={skill.name}
                variants={item}
                whileHover={{ scale: 1.05, y: -2 }}
                onMouseEnter={() => setHoveredSkill(skill.name)}
                onMouseLeave={() => setHoveredSkill(null)}
              >
                <Badge
                  variant="outline"
                  className={`relative cursor-default transition-all duration-300 ${
                    categoryColors[category as keyof typeof categoryColors]
                  } ${hoveredSkill === skill.name ? 'shadow-md' : ''}`}
                >
                  {skill.name}
                  {hoveredSkill === skill.name && (
                    <motion.div
                      className="absolute -bottom-5 left-1/2 transform -translate-x-1/2 bg-background border border-border rounded px-1.5 py-0.5 text-[10px] shadow-sm whitespace-nowrap z-10"
                      initial={{ opacity: 0, y: -5 }}
                      animate={{ opacity: 1, y: 0 }}
                    >
                      Proficiency: {skill.proficiency}%
                    </motion.div>
                  )}
                </Badge>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>
      ))}
    </div>
  )
}
